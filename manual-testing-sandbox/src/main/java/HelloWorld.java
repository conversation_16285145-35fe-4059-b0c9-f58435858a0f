package src.main.java;

// HelloWorld 类演示基本的数学运算和主方法
public class HelloWorld {
    // 主方法，程序入口
    public static void main(String[] args) {
        System.out.println("Hello, World!"); // 输出 Hello, World!
    }

    // 两数相加
    public static int add(int a, int b) {
        return a + b;
    }

    // 两数相乘
    public static int multiply(int a, int b) {
        return a * b;
    }

    // 两数相除，若除数为0则抛出异常
    public static double divide(int a, int b) {
        if (b == 0) {
            throw new ArithmeticException("不允许除以零");
        }
        return (double) a / b;
    }
}
