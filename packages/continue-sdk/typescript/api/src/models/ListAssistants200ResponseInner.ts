/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
import type { ListAssistants200ResponseInnerConfigResult } from "./ListAssistants200ResponseInnerConfigResult";
import {
  ListAssistants200ResponseInnerConfigResultFromJSON,
  ListAssistants200ResponseInnerConfigResultFromJSONTyped,
  ListAssistants200ResponseInnerConfigResultToJSON,
  ListAssistants200ResponseInnerConfigResultToJSONTyped,
} from "./ListAssistants200ResponseInnerConfigResult";

/**
 *
 * @export
 * @interface ListAssistants200ResponseInner
 */
export interface ListAssistants200ResponseInner {
  /**
   *
   * @type {ListAssistants200ResponseInnerConfigResult}
   * @memberof ListAssistants200ResponseInner
   */
  configResult: ListAssistants200ResponseInnerConfigResult;
  /**
   * Slug of the user or organization that owns the assistant
   * @type {string}
   * @memberof ListAssistants200ResponseInner
   */
  ownerSlug: string;
  /**
   * Slug of the assistant package
   * @type {string}
   * @memberof ListAssistants200ResponseInner
   */
  packageSlug: string;
  /**
   * Pre-signed URL for the assistant's icon
   * @type {string}
   * @memberof ListAssistants200ResponseInner
   */
  iconUrl?: string | null;
  /**
   * URL of the on-premises proxy if the organization uses one
   * @type {string}
   * @memberof ListAssistants200ResponseInner
   */
  onPremProxyUrl?: string | null;
  /**
   * Whether the organization uses an on-premises proxy
   * @type {boolean}
   * @memberof ListAssistants200ResponseInner
   */
  useOnPremProxy?: boolean | null;
  /**
   * Raw YAML configuration of the assistant
   * @type {string}
   * @memberof ListAssistants200ResponseInner
   */
  rawYaml?: string;
}

/**
 * Check if a given object implements the ListAssistants200ResponseInner interface.
 */
export function instanceOfListAssistants200ResponseInner(
  value: object,
): value is ListAssistants200ResponseInner {
  if (!("configResult" in value) || value["configResult"] === undefined)
    return false;
  if (!("ownerSlug" in value) || value["ownerSlug"] === undefined) return false;
  if (!("packageSlug" in value) || value["packageSlug"] === undefined)
    return false;
  return true;
}

export function ListAssistants200ResponseInnerFromJSON(
  json: any,
): ListAssistants200ResponseInner {
  return ListAssistants200ResponseInnerFromJSONTyped(json, false);
}

export function ListAssistants200ResponseInnerFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): ListAssistants200ResponseInner {
  if (json == null) {
    return json;
  }
  return {
    configResult: ListAssistants200ResponseInnerConfigResultFromJSON(
      json["configResult"],
    ),
    ownerSlug: json["ownerSlug"],
    packageSlug: json["packageSlug"],
    iconUrl: json["iconUrl"] == null ? undefined : json["iconUrl"],
    onPremProxyUrl:
      json["onPremProxyUrl"] == null ? undefined : json["onPremProxyUrl"],
    useOnPremProxy:
      json["useOnPremProxy"] == null ? undefined : json["useOnPremProxy"],
    rawYaml: json["rawYaml"] == null ? undefined : json["rawYaml"],
  };
}

export function ListAssistants200ResponseInnerToJSON(
  json: any,
): ListAssistants200ResponseInner {
  return ListAssistants200ResponseInnerToJSONTyped(json, false);
}

export function ListAssistants200ResponseInnerToJSONTyped(
  value?: ListAssistants200ResponseInner | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    configResult: ListAssistants200ResponseInnerConfigResultToJSON(
      value["configResult"],
    ),
    ownerSlug: value["ownerSlug"],
    packageSlug: value["packageSlug"],
    iconUrl: value["iconUrl"],
    onPremProxyUrl: value["onPremProxyUrl"],
    useOnPremProxy: value["useOnPremProxy"],
    rawYaml: value["rawYaml"],
  };
}
